.breadcrumb-nav {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  align-self: stretch;
  width: 100%;

  > .back {
    font-size: var(--font-size-text2);
    line-height: var(--font-lineheight-text2);
    color: var(--color-text-secondary);
    @include up-lg { display: none; }
  }

  > .list {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 0;
    font-size: var(--font-size-text2);
    line-height: var(--font-lineheight-text2);

    // Mobile: horizontal scroll instead of wrapping
    @include down-lg {
      flex-wrap: nowrap;
      overflow-x: auto;
      overflow-y: hidden;
      -webkit-overflow-scrolling: touch; // smooth scrolling on iOS
      scrollbar-width: thin; // Firefox

      // Hide scrollbar on webkit browsers but keep functionality
      &::-webkit-scrollbar {
        height: 4px;
      }
      &::-webkit-scrollbar-track {
        background: transparent;
      }
      &::-webkit-scrollbar-thumb {
        background: var(--color-selection, #ccc);
        border-radius: 2px;
      }
      &::-webkit-scrollbar-thumb:hover {
        background: var(--color-text-secondary, #999);
      }
    }

    > .item {
      display: inline-flex;
      align-items: center;
      flex-shrink: 0; // Prevent items from shrinking on mobile scroll

      > .link {
        color: var(--color-text-secondary);
        text-decoration: none;
        font-size: var(--font-size-text2);
        font-weight: var(--font-weight-medium);
        line-height: var(--font-lineheight-text2);
        display: inline-flex;
        align-items: center;

        svg path { fill: currentColor; }

        &:is(:visited, :active) {
          color: var(--color-text-secondary);
          text-decoration: none;
        }

        @include up-lg {
          &:hover { color: var(--color-text); }
        }

        &.-active,
        &[aria-current="page"] { color: var(--color-text); font-weight: var(--font-weight-medium); }
      }

      > .chevron { width: var(--icon-size); height: var(--icon-size); }
    }
  }
}
