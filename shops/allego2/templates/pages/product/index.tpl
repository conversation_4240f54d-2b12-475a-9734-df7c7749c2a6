{{extends file='frame_full.tpl'}}

{{block name='layout_header'}}
  {{include file="components/header/header.tpl" show_promo=false}}
{{/block}}

{{block name='page_content'}}
  {{assign var=has_features value=(isset($features) && $features)}}
  {{assign var=has_unbound value=(isset($features_unbound) && $features_unbound && ($features_unbound|@count > 0))}}
  {{assign var=show_technical_tab value=($has_features || $has_unbound)}}

  <div class="page-product" data-product-id="{{$product.product_id}}">
    <section class="sidebar-layout">
        <aside class="sidebar">
          {{include file='components/layout/category-sidebar.tpl'}}
        </aside>
      <div class="primary">
          {{include file='components/product/product-box.tpl' product=$product}}
          {{if $goodies|@count}}
              <a class="action more-goodies hover-underline" href="#goodies" data-jump="goodies" aria-label="Produkt GOODIES entdecken">
                  <img class="icon" src="{{$_cfg.grafik_url}}icons/goodie-smiley-orange.svg" alt="" width="24" height="24" />
                  <span class="text">Produkt GOODIES entdecken</span>
              </a>
          {{/if}}
          {{* Abstand nur anzeigen wenn keine Goodies vorhanden sind *}}
          {{if !($goodies|@count)}}
            <div class="space-6 space-desktop-8"></div>
          {{/if}}

        <div class="product-tabs">
          <nav class="nav" role="tablist">
            <button class="tab -active" type="button" id="product-tab-description-trigger" data-tab="description" aria-controls="product-tab-description" aria-selected="true">
              <img class="icon" src="{{$_cfg.grafik_url}}icons/product-tab-description.svg" alt="" role="presentation" width="24" height="24" />
              <span class="label">Beschreibung</span>
            </button>
            {{if $show_technical_tab}}
              <button class="tab" type="button" id="product-tab-technical-trigger" data-tab="technical" aria-controls="product-tab-technical" aria-selected="false">
                <img class="icon" src="{{$_cfg.grafik_url}}icons/product-tab-technical.svg" alt="" role="presentation" width="25" height="24" />
                <span class="label">Technische Merkmale</span>
              </button>
            {{/if}}

            {{if isset($datasheet) && $datasheet }}
              <button class="tab" type="button" id="product-tab-datasheet-trigger" data-tab="datasheet" aria-controls="product-tab-datasheet" aria-selected="false">
                <img class="icon" src="{{$_cfg.grafik_url}}icons/product-tab-datasheet.svg" alt="" role="presentation" width="24" height="24" />
                <span class="label">Herstellerdatenblatt</span>
              </button>
            {{/if}}

            {{if $zubehoer}}
              <button class="tab" type="button" id="product-tab-accessories-trigger" data-tab="accessories" aria-controls="product-tab-accessories" aria-selected="false">
                <img class="icon" src="{{$_cfg.grafik_url}}icons/product-tab-accessories.svg" alt="" role="presentation" width="25" height="24" />
                <span class="label">Zubehör</span>
              </button>
            {{/if}}
          </nav>
          <div class="content">
            {{include file='./sections/product-tab-description.tpl'
              product=$product
              order_codes=$order_codes|default:array()
              device_result=$device_result|default:null
              loadbee_integration_code=$loadbee_integration_code|default:''
              has_loadbee=$has_loadbee|default:false
              loadbee_apikey=$loadbee_apikey|default:''
              loadbee_gtin=$loadbee_gtin|default:''
              loadbee_locale=$loadbee_locale|default:''
            }}

            {{if $show_technical_tab}}
              {{include file='./sections/product-tab-technical.tpl'}}
            {{/if}}

            {{*  {{include file='./sections/product-tab-datasheet.tpl'}} *}}

            {{if $zubehoer}}
              {{include file='./sections/product-tab-accessories.tpl' zubehoer=$zubehoer|default:array()}}
            {{/if}}
          </div>
        </div>

        {{* Goodies Sektion - nur anzeigen wenn Goodies vorhanden sind *}}
        {{if $goodies|@count}}
          <div class="space-6 space-desktop-8" id="goodies"></div>

          {{block name='product_goodies'}}
              <h2 class="section-title">Alle Produkt GOODIES smart erklärt</h2>
              {{include file='components/goodie/goodie-list.tpl' layout='list' goodies=$goodies}}
          {{/block}}

          <div class="space-6 space-desktop-8"></div>
        {{/if}}

        <div class="product-details-nav _stack">
          {{if isset($related_combined) && $related_combined|@count }}
            <div class="detail-group">
              {{include file='./sections/product-tab-related.tpl'}}
            </div>
          {{/if}}

          <div class="detail-group">
            {{include file='./sections/product-tab-bewertung.tpl'}}
          </div>

          {{if $product.finanzierung}}
            <div class="detail-group">
              {{include file='./sections/product-tab-financing.tpl'}}
            </div>
          {{/if}}

          <div class="detail-group">
            {{include file='./sections/product-tab-shipping.tpl'}}
          </div>

          {{if $extra_services}}
            <div class="detail-group">
              {{include file='./sections/product-tab-services.tpl'}}
            </div>
          {{/if}}

          {{if $display_config['show_verpackungsentsorgung']}}
            <div class="detail-group">
              {{include file='./sections/product-tab-packaging.tpl'}}
            </div>
          {{/if}}
        </div>

      </div>
    </section>

    <div class="space-6 space-desktop-8"></div>


    {{if !empty($variations)}}
      <script>
        window.variations_init = [{{$product.set_product_id}}, {{$variations->getPreSelectedValuesJsonEncoded()}}, {{$cat_id}}];
      </script>
    {{/if}}

    <script>
      _gaq.push(['_trackPageview', '/virt_trichter1/schritt_product.html']);
    </script>

    {{include file='components/product/product-sticky-cta.tpl' product=$product}}
  </div>
{{/block}}
